# 链接跳转统计功能测试指南

## 功能概述

现在您的mulink应用已经集成了DurableObject统计功能，可以记录：
1. 每个slug的访问次数
2. 每个slug下各个URL的访问次数

## API端点

### 1. 查看所有统计数据
```
GET /api/stats
```

返回格式：
```json
{
  "slugCounts": {
    "example-slug": 10,
    "another-slug": 5
  },
  "urlCounts": {
    "example-slug": {
      "https://example.com": 6,
      "https://backup.com": 4
    },
    "another-slug": {
      "https://another.com": 5
    }
  }
}
```

### 2. 查看特定slug的统计数据
```
GET /api/stats/example-slug
```

返回格式：
```json
{
  "slug": "example-slug",
  "slugCount": 10,
  "urlCounts": {
    "https://example.com": 6,
    "https://backup.com": 4
  }
}
```

### 3. 查看请求日志
```
GET /api/request-logs
GET /api/request-logs?slug=example-slug&limit=20&offset=0
```

返回格式：
```json
{
  "slug": "example-slug",
  "logs": [
    {
      "requestId": "1703123456789-abc123def",
      "url": "https://yourdomain.com/example-slug?utm_source=test",
      "method": "GET",
      "headers": {
        "user-agent": "Mozilla/5.0...",
        "accept": "text/html,application/xhtml+xml...",
        "cf-ray": "123456789abcdef0-SJC"
      },
      "cf": {
        "country": "US",
        "city": "San Francisco",
        "region": "California",
        "timezone": "America/Los_Angeles",
        "asn": 13335
      },
      "timestamp": 1703123456789,
      "targetUrl": "https://example.com"
    }
  ],
  "total": 25,
  "limit": 20,
  "offset": 0
}
```

### 4. 查看特定slug的请求日志
```
GET /api/request-logs/example-slug
GET /api/request-logs/example-slug?limit=10&offset=0
```



## 测试步骤

1. **创建一个链接**：
   ```bash
   curl -X POST http://localhost:8787/api/link \
     -H "Content-Type: application/json" \
     -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     -d '{
       "slug": "test-slug",
       "targets": [
         {"url": "https://example.com", "weight": 70},
         {"url": "https://backup.com", "weight": 30}
       ]
     }'
   ```

2. **访问链接几次**：
   ```bash
   curl http://localhost:8787/test-slug
   curl http://localhost:8787/test-slug
   curl http://localhost:8787/test-slug
   ```

3. **查看统计数据**：
   ```bash
   # 查看所有统计
   curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     http://localhost:8787/api/stats

   # 查看特定slug统计
   curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     http://localhost:8787/api/stats/test-slug
   ```

4. **查看请求日志**：
   ```bash
   # 查看所有请求日志（需要认证）
   curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     http://localhost:8787/api/request-logs

   # 查看特定slug的请求日志（需要认证）
   curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     http://localhost:8787/api/request-logs/test-slug

   # 分页查看请求日志（需要认证）
   curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     "http://localhost:8787/api/request-logs?limit=10&offset=0"
   ```

5. **删除链接和所有相关数据**：
   ```bash
   # 删除链接时会自动删除对应的统计数据和请求日志
   curl -X DELETE \
     -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     http://localhost:8787/api/link/test-slug

   # 验证统计数据和请求日志都被删除
   curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     http://localhost:8787/api/stats/test-slug
   curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     http://localhost:8787/api/request-logs/test-slug
   ```

## 实现细节

- 使用DurableObject确保统计数据和请求日志的一致性和持久性
- 统计记录和请求日志记录都是异步的，不会影响链接跳转的响应速度
- 所有数据存储在同一个DurableObject实例中（使用"global"作为ID）
- **优化**：合并了slug和URL统计为一个方法，使用事务确保数据一致性
- 数据格式：
  - slug计数：`slug:{slug_name}` -> count
  - URL计数：`url:{slug_name}:{url}` -> count
  - 请求日志：`request:{slug_name}:{request_id}` -> requestInfo

## 请求日志功能

### 记录的信息
- **URL**: 完整的请求URL（包括查询参数）
- **Method**: HTTP方法（通常是GET）
- **Headers**: 所有请求头信息
- **CF对象**: Cloudflare提供的请求信息（国家、城市、ASN等）
- **Timestamp**: 请求时间戳
- **Target URL**: 实际跳转的目标URL

### 查询功能
- 支持按slug过滤请求日志
- 支持分页查询（limit和offset参数）
- 提供总数统计信息
### 接口权限
- **所有接口**（`/api/*`）：需要基本认证

## 路由说明

### 短链接访问
- `/:slug` - 短链接访问（通配符路由）
- 所有短链接访问都会记录统计数据和请求日志

### API接口（需要认证）
- `/api/link/*` - 链接管理接口
- `/api/stats/*` - 统计数据接口
- `/api/request-logs/*` - 请求日志接口

## 注意事项

- 统计功能已经集成到现有的链接跳转逻辑中
- 每次访问链接时会自动记录slug和最终选择的URL的访问次数
- 统计数据通过DurableObject持久化存储
- API端点需要基本认证（与其他API端点相同）

## 修复的问题

### 重复计数问题修复
- **问题**：之前可能出现双倍计数或多次计数的问题
- **原因**：
  1. 路由重叠：多个路由可能同时匹配同一个请求
  2. 返回方式错误：返回文本URL而不是HTTP重定向，可能导致客户端额外请求
- **解决方案**：
  1. 简化为单一通配符路由 `/*` 处理所有短链接访问
  2. 改为返回 `c.redirect(finalUrl, 302)` 进行真正的HTTP重定向
  3. 确保每个slug访问只记录一次统计

### 优化后的特性
- ✅ 避免路由重叠导致的重复计数
- ✅ 使用HTTP 302重定向，符合短链接服务标准
- ✅ 统一的处理逻辑，减少代码重复
- ✅ 事务保证slug和URL计数的原子性更新
- ✅ 删除链接时自动清理对应的统计数据和请求日志
- ✅ 详细的请求日志记录，包含完整的请求信息和CF数据

## 数据一致性保证

### 删除操作
- 删除短链接时会自动删除所有相关数据：
  - slug访问次数统计
  - 所有URL访问次数统计
  - 所有相关的请求日志
- 使用事务确保删除操作的原子性
- 即使统计删除失败，也不会影响链接删除的成功

### 错误处理
- 删除不存在的链接会返回404错误
- 统计和日志删除失败会记录错误日志但不影响主要操作
- 保证数据的一致性和系统的健壮性

### 性能考虑
- 请求日志可能会快速增长，建议定期清理旧数据
- 分页查询避免一次性加载大量日志数据
- 异步记录确保不影响用户体验
