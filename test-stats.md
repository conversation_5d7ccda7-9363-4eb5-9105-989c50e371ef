# 基于Cloudflare Workers Observability的链接分析指南

## 功能概述

您的mulink应用现在完全基于Cloudflare Workers Observability进行数据分析：
1. **结构化日志记录**：记录所有关键业务事件
2. **强大的查询分析**：通过Query Builder进行复杂数据分析
3. **实时监控**：Dashboard提供实时指标和可视化图表

## 记录的事件类型

### 1. 链接跳转事件 (link_redirect)
```json
{
  "event_type": "link_redirect",
  "slug": "example-slug",
  "target_url": "https://example.com",
  "final_url": "https://example.com?utm_source=test",
  "timestamp": 1703123456789,
  "user_agent": "Mozilla/5.0...",
  "country": "US",
  "city": "San Francisco",
  "referer": "https://google.com",
  "search_params": {
    "utm_source": "test",
    "utm_medium": "social"
  }
}
```

### 2. 链接未找到事件 (link_not_found)
```json
{
  "event_type": "link_not_found",
  "slug": "nonexistent-slug",
  "timestamp": 1703123456789,
  "user_agent": "Mozilla/5.0...",
  "country": "US",
  "city": "San Francisco",
  "referer": "https://google.com"
}
```

### 3. 链接管理事件
- **link_created**: 创建新链接
- **link_updated**: 更新链接目标
- **link_deleted**: 删除链接
- **homepage_visit**: 主页访问



## 使用Cloudflare Observability查询数据

### 1. 访问Observability Dashboard
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 进入 **Workers & Pages** > **Observability**
3. 选择您的Worker
4. 使用 **Query Builder** 进行数据分析

### 2. 常用查询示例

#### 查询slug访问统计
```sql
SELECT
  slug,
  COUNT(*) as total_clicks,
  COUNT(DISTINCT country) as countries_reached
FROM logs
WHERE event_type = 'link_redirect'
  AND timestamp >= NOW() - INTERVAL '7 days'
GROUP BY slug
ORDER BY total_clicks DESC
```

#### 查询URL分布
```sql
SELECT
  slug,
  target_url,
  COUNT(*) as clicks,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY slug), 2) as percentage
FROM logs
WHERE event_type = 'link_redirect'
  AND slug = 'your-slug-here'
  AND timestamp >= NOW() - INTERVAL '30 days'
GROUP BY slug, target_url
ORDER BY clicks DESC
```

#### 查询地理分布
```sql
SELECT
  country,
  city,
  COUNT(*) as clicks
FROM logs
WHERE event_type = 'link_redirect'
  AND timestamp >= NOW() - INTERVAL '7 days'
GROUP BY country, city
ORDER BY clicks DESC
LIMIT 20
```

#### 查询时间趋势
```sql
SELECT
  DATE(timestamp) as date,
  COUNT(*) as daily_clicks
FROM logs
WHERE event_type = 'link_redirect'
  AND timestamp >= NOW() - INTERVAL '30 days'
GROUP BY DATE(timestamp)
ORDER BY date DESC
```

#### 查询404错误
```sql
SELECT
  slug,
  COUNT(*) as not_found_count,
  COUNT(DISTINCT user_agent) as unique_visitors
FROM logs
WHERE event_type = 'link_not_found'
  AND timestamp >= NOW() - INTERVAL '7 days'
GROUP BY slug
ORDER BY not_found_count DESC
```

## 实现细节

### 完全基于Observability的架构
- **移除了DurableObject**：不再需要自定义统计存储
- **结构化日志记录**：所有业务事件都通过console.log记录
- **零维护成本**：Cloudflare负责日志收集、存储和索引
- **实时分析**：通过Query Builder进行复杂数据分析

### 配置说明
```jsonc
{
  "observability": {
    "enabled": true,
    "logs": {
      "invocation_logs": true,
      "head_sampling_rate": 1
    }
  }
}
```

### 日志数据结构
每个事件都包含以下标准字段：
- `event_type`: 事件类型标识
- `timestamp`: 时间戳
- `user_agent`: 用户代理
- `country`: 国家代码
- `city`: 城市名称
- `referer`: 来源页面

### 优势对比
| 特性 | 旧方案(DurableObject) | 新方案(Observability) |
|------|---------------------|---------------------|
| 代码复杂度 | 高(800+行) | 低(200行) |
| 维护成本 | 需要维护存储逻辑 | 零维护 |
| 查询能力 | 基础统计 | 强大的SQL查询 |
| 数据保留 | 永久(需付费) | 7天免费 |
| 可视化 | 需自建 | 内置Dashboard |
| 地理分析 | 需额外实现 | 自动包含 |
| 实时性 | 需要API调用 | 实时查询 |

### 测试步骤
1. **创建链接并访问**：
   ```bash
   # 创建链接
   curl -X POST http://localhost:8787/api/link \
     -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
     -H "Content-Type: application/json" \
     -d '{"slug": "test", "targets": [{"url": "https://example.com", "weight": 100}]}'

   # 访问链接生成日志
   curl -L http://localhost:8787/test
   ```

2. **查看日志**：
   - 访问Cloudflare Dashboard
   - 进入Workers & Pages > Observability
   - 查看实时日志和使用Query Builder分析数据

## 路由说明

### 短链接访问
- `/:slug` - 短链接访问（通配符路由）
- 所有短链接访问都会记录统计数据和请求日志

### API接口（需要认证）
- `/api/link/*` - 链接管理接口
- `/api/stats/*` - 统计数据接口
- `/api/request-logs/*` - 请求日志接口

## 注意事项

- 统计功能已经集成到现有的链接跳转逻辑中
- 每次访问链接时会自动记录slug和最终选择的URL的访问次数
- 统计数据通过DurableObject持久化存储
- API端点需要基本认证（与其他API端点相同）

## 修复的问题

### 重复计数问题修复
- **问题**：之前可能出现双倍计数或多次计数的问题
- **原因**：
  1. 路由重叠：多个路由可能同时匹配同一个请求
  2. 返回方式错误：返回文本URL而不是HTTP重定向，可能导致客户端额外请求
- **解决方案**：
  1. 简化为单一通配符路由 `/*` 处理所有短链接访问
  2. 改为返回 `c.redirect(finalUrl, 302)` 进行真正的HTTP重定向
  3. 确保每个slug访问只记录一次统计

### 优化后的特性
- ✅ 避免路由重叠导致的重复计数
- ✅ 使用HTTP 302重定向，符合短链接服务标准
- ✅ 统一的处理逻辑，减少代码重复
- ✅ 事务保证slug和URL计数的原子性更新
- ✅ 删除链接时自动清理对应的统计数据和请求日志
- ✅ 详细的请求日志记录，包含完整的请求信息和CF数据

## 数据一致性保证

### 删除操作
- 删除短链接时会自动删除所有相关数据：
  - slug访问次数统计
  - 所有URL访问次数统计
  - 所有相关的请求日志
- 使用事务确保删除操作的原子性
- 即使统计删除失败，也不会影响链接删除的成功

### 错误处理
- 删除不存在的链接会返回404错误
- 统计和日志删除失败会记录错误日志但不影响主要操作
- 保证数据的一致性和系统的健壮性

### 性能考虑
- 请求日志可能会快速增长，建议定期清理旧数据
- 分页查询避免一次性加载大量日志数据
- 异步记录确保不影响用户体验
