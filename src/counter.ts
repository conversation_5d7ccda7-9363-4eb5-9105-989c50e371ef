import { DurableObject } from "cloudflare:workers";

export class Counter extends DurableObject {
  constructor(ctx: DurableObjectState, env: CloudflareBindings) {
    super(ctx, env);
  }

  // 处理HTTP请求
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    if (request.method === "POST" && path === "/increment") {
      return this.incrementCounts(request);
    } else if (request.method === "POST" && path === "/log-request") {
      return this.logRequest(request);
    } else if (request.method === "GET" && path === "/stats") {
      return this.getStats(request);
    } else if (request.method === "GET" && path === "/request-logs") {
      return this.getRequestLogs(request);
    } else if (path.startsWith("/request-detail/")) {
      const requestId = path.replace("/request-detail/", "");
      return this.getRequestDetail(requestId);
    } else if (request.method === "DELETE" && path === "/delete-stats") {
      return this.deleteStats(request);
    }

    return new Response("Not found", { status: 404 });
  }

  // 同时增加slug和URL访问次数
  private async incrementCounts(request: Request): Promise<Response> {
    try {
      const { slug, url } = await request.json() as { slug: string; url: string };
      if (!slug || !url) {
        return new Response("Missing slug or url", { status: 400 });
      }

      // 使用事务确保数据一致性
      await this.ctx.storage.transaction(async (txn) => {
        // 增加slug访问次数
        const slugKey = `slug:${slug}`;
        const slugCount = await txn.get<number>(slugKey) || 0;
        await txn.put(slugKey, slugCount + 1);

        // 增加URL访问次数
        const urlKey = `url:${slug}:${url}`;
        const urlCount = await txn.get<number>(urlKey) || 0;
        await txn.put(urlKey, urlCount + 1);
      });

      return new Response(JSON.stringify({
        slug,
        url,
        success: true
      }), {
        headers: { "Content-Type": "application/json" }
      });
    } catch (error) {
      return new Response("Invalid request", { status: 400 });
    }
  }

  // 记录请求信息
  private async logRequest(request: Request): Promise<Response> {
    try {
      const { slug, requestInfo } = await request.json() as {
        slug: string;
        requestInfo: {
          url: string;
          method: string;
          headers: Record<string, string>;
          cf: any;
          timestamp: number;
        }
      };

      if (!slug || !requestInfo) {
        return new Response("Missing slug or requestInfo", { status: 400 });
      }

      // 生成唯一的请求ID
      const requestId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const logKey = `request:${slug}:${requestId}`;

      // 存储请求信息
      await this.ctx.storage.put(logKey, requestInfo);

      return new Response(JSON.stringify({
        slug,
        requestId,
        success: true
      }), {
        headers: { "Content-Type": "application/json" }
      });
    } catch (error) {
      return new Response("Invalid request", { status: 400 });
    }
  }

  // 获取请求日志
  private async getRequestLogs(request: Request): Promise<Response> {
    try {
      const url = new URL(request.url);
      const slug = url.searchParams.get("slug");
      const limit = parseInt(url.searchParams.get("limit") || "50");
      const offset = parseInt(url.searchParams.get("offset") || "0");

      if (slug) {
        // 获取特定slug的请求日志
        return this.getSlugRequestLogs(slug, limit, offset);
      } else {
        // 获取所有请求日志
        return this.getAllRequestLogs(limit, offset);
      }
    } catch (error) {
      return new Response("Error getting request logs", { status: 500 });
    }
  }

  // 获取统计数据
  private async getStats(request: Request): Promise<Response> {
    try {
      const url = new URL(request.url);
      const slug = url.searchParams.get("slug");

      if (slug) {
        // 获取特定slug的统计数据
        return this.getSlugStats(slug);
      } else {
        // 获取所有统计数据
        return this.getAllStats();
      }
    } catch (error) {
      return new Response("Error getting stats", { status: 500 });
    }
  }

  // 获取特定slug的统计数据
  private async getSlugStats(slug: string): Promise<Response> {
    const slugKey = `slug:${slug}`;
    const slugCount = await this.ctx.storage.get<number>(slugKey) || 0;

    // 获取该slug下所有URL的统计
    const urlPrefix = `url:${slug}:`;
    const urlStats = await this.ctx.storage.list<number>({ prefix: urlPrefix });

    const urlCounts: Record<string, number> = {};
    for (const [key, count] of urlStats) {
      const url = key.replace(urlPrefix, "");
      urlCounts[url] = count;
    }

    return new Response(JSON.stringify({
      slug,
      slugCount,
      urlCounts
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }

  // 获取所有统计数据
  private async getAllStats(): Promise<Response> {
    const allData = await this.ctx.storage.list<number>();

    const slugCounts: Record<string, number> = {};
    const urlCounts: Record<string, Record<string, number>> = {};

    for (const [key, count] of allData) {
      if (key.startsWith("slug:")) {
        const slug = key.replace("slug:", "");
        slugCounts[slug] = count;
      } else if (key.startsWith("url:")) {
        const parts = key.replace("url:", "").split(":");
        if (parts.length >= 2) {
          const slug = parts[0];
          const url = parts.slice(1).join(":");
          if (!urlCounts[slug]) {
            urlCounts[slug] = {};
          }
          urlCounts[slug][url] = count;
        }
      }
    }

    return new Response(JSON.stringify({
      slugCounts,
      urlCounts
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }

  // 删除特定slug的所有统计数据
  private async deleteStats(request: Request): Promise<Response> {
    try {
      const { slug } = await request.json() as { slug: string };
      if (!slug) {
        return new Response("Missing slug", { status: 400 });
      }

      // 使用事务删除所有相关的统计数据和请求日志
      await this.ctx.storage.transaction(async (txn) => {
        // 删除slug统计
        const slugKey = `slug:${slug}`;
        await txn.delete(slugKey);

        // 查找并删除所有相关的URL统计
        const urlPrefix = `url:${slug}:`;
        const urlStats = await txn.list<number>({ prefix: urlPrefix });

        // 查找并删除所有相关的请求日志
        const requestPrefix = `request:${slug}:`;
        const requestLogs = await txn.list({ prefix: requestPrefix });

        const keysToDelete: string[] = [];
        for (const [key] of urlStats) {
          keysToDelete.push(key);
        }
        for (const [key] of requestLogs) {
          keysToDelete.push(key);
        }

        // 批量删除统计和日志
        if (keysToDelete.length > 0) {
          await txn.delete(keysToDelete);
        }
      });

      return new Response(JSON.stringify({
        slug,
        deleted: true
      }), {
        headers: { "Content-Type": "application/json" }
      });
    } catch (error) {
      return new Response("Invalid request", { status: 400 });
    }
  }

  // 获取特定slug的请求日志
  private async getSlugRequestLogs(slug: string, limit: number, offset: number): Promise<Response> {
    const requestPrefix = `request:${slug}:`;
    const requestLogs = await this.ctx.storage.list({ prefix: requestPrefix, limit: limit + offset });

    const logs: any[] = [];
    let count = 0;
    for (const [key, value] of requestLogs) {
      if (count >= offset && logs.length < limit) {
        const requestId = key.replace(requestPrefix, "");
        logs.push({
          requestId,
          ...(value as Record<string, any>)
        });
      }
      count++;
    }

    return new Response(JSON.stringify({
      slug,
      logs,
      total: count,
      limit,
      offset
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }

  // 获取所有请求日志
  private async getAllRequestLogs(limit: number, offset: number): Promise<Response> {
    const allLogs = await this.ctx.storage.list({ prefix: "request:", limit: limit + offset });

    const logs: any[] = [];
    let count = 0;
    for (const [key, value] of allLogs) {
      if (count >= offset && logs.length < limit) {
        const parts = key.replace("request:", "").split(":");
        if (parts.length >= 2) {
          const slug = parts[0];
          const requestId = parts.slice(1).join(":");
          logs.push({
            slug,
            requestId,
            ...(value as Record<string, any>)
          });
        }
      }
      count++;
    }

    return new Response(JSON.stringify({
      logs,
      total: count,
      limit,
      offset
    }), {
      headers: { "Content-Type": "application/json" }
    });
  }

  // 根据requestId获取请求详情
  private async getRequestDetail(requestId: string): Promise<Response> {
    try {
      // 搜索包含该requestId的所有键
      const allLogs = await this.ctx.storage.list({ prefix: "request:" });

      for (const [key, value] of allLogs) {
        if (key.endsWith(`:${requestId}`)) {
          const parts = key.replace("request:", "").split(":");
          if (parts.length >= 2) {
            const slug = parts[0];
            const foundRequestId = parts.slice(1).join(":");

            if (foundRequestId === requestId) {
              return new Response(JSON.stringify({
                requestId,
                slug,
                ...(value as Record<string, any>)
              }), {
                headers: { "Content-Type": "application/json" }
              });
            }
          }
        }
      }

      return new Response("Request not found", { status: 404 });
    } catch (error) {
      return new Response("Error getting request detail", { status: 500 });
    }
  }
}
