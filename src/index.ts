import { Hono } from "hono";
import { cors } from "hono/cors";
import { basicAuth } from "hono/basic-auth";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

interface CloudflareBindings {
  KV: KVNamespace;
}

const app = new Hono<{ Bindings: CloudflareBindings }>();

app.use(
  "/api/*",
  cors({
    origin: (origin) => {
      return origin.endsWith(".melook.app") ||
        origin.includes("localhost") ||
        origin.includes("vusercontent.net")
        ? origin
        : "https://google.com";
    },
    allowMethods: ["GET", "HEAD", "POST", "PUT", "DELETE"],
  })
);

const username = "MULINK";
const password = "TJNIUBI666";

app.use("/api/*", basicAuth({ username, password }));

// 创建短链接
app.post(
  "/api/link",
  zValidator(
    "json",
    z.object({
      slug: z.string(),
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const { slug, targets } = c.req.valid("json");
    await c.env.KV.put(slug, JSON.stringify(targets));
    return c.text("OK");
  }
);

// 获取短链接信息
app.get("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }
  return c.json(JSON.parse(targets));
});

// 删除短链接
app.delete("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  await c.env.KV.delete(slug);
  return c.text("OK");
});

// 主页
app.get("/", async (c) => {
  return c.text("MuLink - 短链接服务");
});

// 通配符路由处理所有slug访问
app.get("/*", async (c) => {
  const url = new URL(c.req.url);
  const slug = url.pathname.slice(1);

  // 跳过空路径和已知路径
  if (!slug || slug.startsWith("api/") || slug.startsWith(".")) {
    return c.text("Not found", 404);
  }

  return handleSlugRedirect(c, slug);
});

function weightPick(targets: { url: string; weight: number }[]) {
  const totalWeight = targets.reduce((acc, cur) => acc + cur.weight, 0);
  const random = Math.random() * totalWeight;
  let currentWeight = 0;
  for (const target of targets) {
    currentWeight += target.weight;
    if (random <= currentWeight) {
      return target.url;
    }
  }
  return targets[0].url;
}

function mergeSearchParams(originalUrl: string, targetUrl: string) {
  const original = new URL(originalUrl);
  const target = new URL(targetUrl);

  for (const [key, value] of original.searchParams) {
    target.searchParams.set(key, value);
  }

  return target.toString();
}

// 通用的链接处理函数
async function handleSlugRedirect(c: any, slug: string) {
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }

  const targetsJson = JSON.parse(targets) as { url: string; weight: number }[];
  const pickedUrl = weightPick(targetsJson);
  const finalUrl = mergeSearchParams(c.req.url, pickedUrl);

  // 返回HTTP重定向
  return c.redirect(finalUrl, 302);
}

export default app;
