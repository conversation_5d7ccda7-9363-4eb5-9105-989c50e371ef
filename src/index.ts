import { Hono } from "hono";
import { cors } from "hono/cors";
import { basicAuth } from "hono/basic-auth";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";
import { Counter } from "./counter";

const app = new Hono<{ Bindings: CloudflareBindings }>();

app.use(
  "/api/*",
  cors({
    origin: (origin) => {
      return origin.endsWith(".melook.app") ||
        origin.includes("localhost") ||
        origin.includes("vusercontent.net")
        ? origin
        : "https://google.com";
    },
    allowMethods: ["GET", "HEAD", "POST", "PUT", "DELETE"],
  })
);

app.use(
  "/api/*",
  basicAuth({
    username: "MULIN<PERSON>",
    password: "TJNIUBI666",
  })
);

app.post(
  "/api/link",
  zValidator(
    "json",
    z.object({
      slug: z.string(),
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const json = c.req.valid("json");
    // 重复校验
    if (await c.env.KV.get(json.slug)) {
      return c.text("Already exists", 400);
    }
    await c.env.KV.put(json.slug, JSON.stringify(json.targets), {
      metadata: { createdAt: Date.now() },
    });
    return c.text("OK");
  }
);

app.get("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }

  const linkStats = await c.env.COUNTER.get(
    c.env.COUNTER.idFromName("global")
  ).fetch(new Request(`http://counter/stats?slug=${encodeURIComponent(slug)}`));

  return c.json({
    targets: JSON.parse(targets),
    stats: await linkStats.json(),
  });
});

app.put(
  "/api/link/:slug",
  zValidator(
    "json",
    z.object({
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const slug = c.req.param("slug");
    const json = c.req.valid("json");
    // 存在校验
    if (!(await c.env.KV.get(slug))) {
      return c.text("Not found", 404);
    }
    await c.env.KV.put(slug, JSON.stringify(json.targets));
    return c.text("OK");
  }
);

app.delete("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");

  // 检查链接是否存在
  const existingLink = await c.env.KV.get(slug);
  if (!existingLink) {
    return c.text("Not found", 404);
  }

  // 删除KV中的链接数据
  await c.env.KV.delete(slug);

  // 同时删除统计数据
  const counterId = c.env.COUNTER.idFromName("global");
  const counterStub = c.env.COUNTER.get(counterId);

  try {
    await counterStub.fetch(
      new Request("http://counter/delete-stats", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ slug }),
      })
    );
  } catch (error) {
    // 即使统计删除失败，也不影响链接删除的成功
    console.error("Failed to delete stats for slug:", slug, error);
  }

  return c.text("OK");
});

app.get("/api/links", async (c) => {
  const links = await c.env.KV.list<{ createdAt: number }>();

  return c.json({
    links: links.keys.map((key) => ({
      slug: key.name,
      createdAt: key.metadata?.createdAt,
    })),
  });
});

// 获取统计数据的API端点
app.get("/api/stats", async (c) => {
  const slug = c.req.query("slug");

  const counterId = c.env.COUNTER.idFromName("global");
  const counterStub = c.env.COUNTER.get(counterId);

  const statsUrl = slug
    ? `http://counter/stats?slug=${encodeURIComponent(slug)}`
    : "http://counter/stats";

  const response = await counterStub.fetch(new Request(statsUrl));
  const stats = (await response.json()) as Record<string, any>;

  return c.json(stats);
});

// 获取特定slug的统计数据
app.get("/api/stats/:slug", async (c) => {
  const slug = c.req.param("slug");

  const counterId = c.env.COUNTER.idFromName("global");
  const counterStub = c.env.COUNTER.get(counterId);

  const response = await counterStub.fetch(
    new Request(`http://counter/stats?slug=${encodeURIComponent(slug)}`)
  );
  const stats = (await response.json()) as Record<string, any>;

  return c.json(stats);
});



app.get("/", async (c) => {
  return c.redirect("https://fbi.gov");
});

// 通配符路由处理所有slug访问
app.get("/*", async (c) => {
  const url = new URL(c.req.url);
  const slug = url.pathname.slice(1);

  // 跳过空路径和已知路径
  if (!slug || slug.startsWith("api/") || slug.startsWith(".")) {
    return c.text("Not found", 404);
  }

  return handleSlugRedirect(c, slug);
});

function weightPick(targets: { url: string; weight: number }[]) {
  const totalWeight = targets.reduce((acc, cur) => acc + cur.weight, 0);
  const random = Math.random() * totalWeight;
  let currentWeight = 0;
  for (const target of targets) {
    currentWeight += target.weight;
    if (random < currentWeight) {
      return target.url;
    }
  }
  return targets[targets.length - 1].url;
}

function mergeSearchParams(from: string, to: string) {
  const toUrl = new URL(to);
  new URL(from).searchParams.forEach((value, key) => {
    toUrl.searchParams.set(key, value);
  });
  return toUrl.toString();
}

// 通用的链接处理函数
async function handleSlugRedirect(c: any, slug: string) {
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }

  const targetsJson = JSON.parse(targets) as { url: string; weight: number }[];
  const pickedUrl = weightPick(targetsJson);
  const finalUrl = mergeSearchParams(c.req.url, pickedUrl);

  // 获取Counter实例
  const counterId = c.env.COUNTER.idFromName("global");
  const counterStub = c.env.COUNTER.get(counterId);

  // 异步记录访问统计，不阻塞响应
  c.executionCtx.waitUntil(
    counterStub.fetch(
      new Request("http://counter/increment", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ slug, url: pickedUrl }),
      })
    ).catch((error: any) => {
      console.error("Failed to record stats:", error);
    })
  );

  // 返回HTTP重定向而不是文本URL
  return c.redirect(finalUrl, 302);
}

export default app;
export { Counter };
