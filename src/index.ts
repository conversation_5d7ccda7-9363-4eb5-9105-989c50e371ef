import { Hono } from "hono";
import { cors } from "hono/cors";
import { basicAuth } from "hono/basic-auth";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

interface CloudflareBindings {
  KV: KVNamespace;
  // Cloudflare API配置
  CLOUDFLARE_ACCOUNT_ID?: string;
  CLOUDFLARE_API_TOKEN?: string;
}

const app = new Hono<{ Bindings: CloudflareBindings }>();

app.use(
  "/api/*",
  cors({
    origin: (origin) => {
      return origin.endsWith(".melook.app") ||
        origin.includes("localhost") ||
        origin.includes("vusercontent.net")
        ? origin
        : "https://google.com";
    },
    allowMethods: ["GET", "HEAD", "POST", "PUT", "DELETE"],
  })
);

app.use(
  "/api/*",
  basicAuth({
    username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    password: "TJNIUBI666",
  })
);

app.post(
  "/api/link",
  zValidator(
    "json",
    z.object({
      slug: z.string(),
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const json = c.req.valid("json");
    // 重复校验
    if (await c.env.KV.get(json.slug)) {
      return c.text("Already exists", 400);
    }

    // 记录链接创建事件
    console.log({
      event_type: "link_created",
      slug: json.slug,
      target_count: json.targets.length,
      targets: json.targets.map((t) => t.url),
      timestamp: Date.now(),
      user_agent: c.req.header("user-agent"),
    });

    await c.env.KV.put(json.slug, JSON.stringify(json.targets), {
      metadata: { createdAt: Date.now() },
    });
    return c.text("OK");
  }
);

app.get("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }

  return c.json({
    slug,
    targets: JSON.parse(targets),
  });
});

app.put(
  "/api/link/:slug",
  zValidator(
    "json",
    z.object({
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const slug = c.req.param("slug");
    const json = c.req.valid("json");
    // 存在校验
    if (!(await c.env.KV.get(slug))) {
      return c.text("Not found", 404);
    }

    // 记录链接更新事件
    console.log({
      event_type: "link_updated",
      slug,
      target_count: json.targets.length,
      targets: json.targets.map((t) => t.url),
      timestamp: Date.now(),
      user_agent: c.req.header("user-agent"),
    });

    await c.env.KV.put(slug, JSON.stringify(json.targets));
    return c.text("OK");
  }
);

app.delete("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");

  // 检查链接是否存在
  const existingLink = await c.env.KV.get(slug);
  if (!existingLink) {
    return c.text("Not found", 404);
  }

  // 删除KV中的链接数据
  await c.env.KV.delete(slug);

  // 记录链接删除事件
  console.log({
    event_type: "link_deleted",
    slug,
    timestamp: Date.now(),
    user_agent: c.req.header("user-agent"),
  });

  return c.text("OK");
});

app.get("/api/links", async (c) => {
  const links = await c.env.KV.list<{ createdAt: number }>();

  return c.json({
    links: links.keys.map((key) => ({
      slug: key.name,
      createdAt: key.metadata?.createdAt,
    })),
  });
});

// 验证API权限
app.get("/api/verify-permissions", async (c) => {
  const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
  const apiToken = c.env.CLOUDFLARE_API_TOKEN;

  if (!accountId || !apiToken) {
    return c.json({ error: "Missing credentials" }, 400);
  }

  const results = {
    token_preview:
      apiToken.substring(0, 8) +
      "..." +
      apiToken.substring(apiToken.length - 4),
    account_id: accountId,
    tests: {} as any,
  };

  // 测试1: 基本账户访问
  try {
    const accountResult = await callCloudflareAPI(
      "",
      accountId,
      apiToken,
      "GET"
    );
    results.tests.account_access = {
      success: !!accountResult.success,
      account_name: accountResult.result?.name,
      error: accountResult.success ? null : "Failed to access account",
    };
  } catch (error: any) {
    results.tests.account_access = {
      success: false,
      error: error.message,
    };
  }

  // 测试2: Workers API访问
  try {
    const workersResult = await callCloudflareAPI(
      "/workers/scripts",
      accountId,
      apiToken,
      "GET"
    );
    results.tests.workers_access = {
      success: !!workersResult.success,
      workers_count: workersResult.result?.length || 0,
      error: workersResult.success ? null : "Failed to access workers",
    };
  } catch (error: any) {
    results.tests.workers_access = {
      success: false,
      error: error.message,
    };
  }

  // 测试3: Observability API访问
  try {
    await callCloudflareAPI(
      "/workers/observability/telemetry/keys",
      accountId,
      apiToken,
      "POST",
      {}
    );
    results.tests.observability_access = {
      success: true,
      error: null,
    };
  } catch (error: any) {
    results.tests.observability_access = {
      success: false,
      error: error.message,
    };
  }

  return c.json({
    ...results,
    summary: {
      all_working: Object.values(results.tests).every(
        (test: any) => test.success
      ),
      required_permissions: [
        "Account - Cloudflare Workers:Edit (基本权限)",
        "Account - Workers Observability:Write (日志权限)",
      ],
    },
  });
});

// 获取所有请求日志
app.get("/api/logs", async (c) => {
  try {
    const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
    const apiToken = c.env.CLOUDFLARE_API_TOKEN;

    if (!accountId || !apiToken) {
      return c.json(
        {
          error: "Cloudflare API credentials not configured",
        },
        500
      );
    }

    // 验证Account ID格式（应该是32位十六进制字符串）
    if (!/^[a-f0-9]{32}$/i.test(accountId)) {
      return c.json(
        {
          error: "Invalid Account ID format",
        },
        400
      );
    }

    // 获取查询参数
    const limit = parseInt(c.req.query("limit") || "50");
    const offset = parseInt(c.req.query("offset") || "0");
    const slug = c.req.query("slug");
    const eventType = c.req.query("event_type");
    const hours = parseInt(c.req.query("hours") || "24");

    // 构建过滤条件
    const filters: Array<{ key: string; operator: string; value: any }> = [];

    if (slug) {
      filters.push({
        key: "slug",
        operator: "equals",
        value: slug,
      });
    }

    if (eventType) {
      filters.push({
        key: "event_type",
        operator: "equals",
        value: eventType,
      });
    }

    // 构建查询
    const query = {
      limit,
      offset,
      timeRange: {
        start: new Date(Date.now() - hours * 60 * 60 * 1000).toISOString(),
        end: new Date().toISOString(),
      },
      filters,
      orderBy: {
        field: "timestamp",
        direction: "desc" as const,
      },
    };

    try {
      // 转换时间格式为Unix时间戳
      const timeframe = {
        from: Math.floor(new Date(query.timeRange.start).getTime() / 1000),
        to: Math.floor(new Date(query.timeRange.end).getTime() / 1000),
      };

      // 尝试获取实际的日志事件数据
      // 首先获取可用的keys
      const keysResult = await callCloudflareAPI(
        "/workers/observability/telemetry/keys",
        accountId,
        apiToken,
        "POST",
        {
          from: timeframe.from,
          to: timeframe.to
        }
      );

      // 查找我们感兴趣的字段
      const availableKeys = keysResult.result || [];
      const eventTypeKey = availableKeys.find((k: any) => k.key === "event_type");
      const slugKey = availableKeys.find((k: any) => k.key === "slug");

      // 使用Cloudflare Workers原生日志查询
      let logs: any[] = [];
      let total = 0;

      try {
        // 使用Cloudflare Logpush API查询Worker日志
        const logpushResult = await callCloudflareAPI(
          `/accounts/${accountId}/logpush/jobs`,
          accountId,
          apiToken,
          "GET"
        );

        if (logpushResult.success) {
          // 如果有Logpush配置，尝试查询日志
          console.log("Logpush jobs found:", logpushResult.result?.length || 0);
        }

        // 尝试使用Workers Analytics API
        const analyticsQuery = {
          query: `
            SELECT
              timestamp,
              scriptName,
              outcome,
              cpuTime,
              duration
            FROM WorkerInvocationsAdaptiveGroups
            WHERE
              date >= '${query.timeRange.start.split('T')[0]}'
              AND date <= '${query.timeRange.end.split('T')[0]}'
              AND scriptName = 'mulink'
            ORDER BY timestamp DESC
            LIMIT ${limit}
            OFFSET ${offset}
          `
        };

        const analyticsResult = await callCloudflareAPI(
          `/accounts/${accountId}/analytics_engine/sql`,
          accountId,
          apiToken,
          "POST",
          { sql: analyticsQuery.query }
        );

        if (analyticsResult.success && analyticsResult.result?.data) {
          logs = analyticsResult.result.data.map((row: any, index: number) => ({
            id: `worker_${Date.now()}_${index}`,
            timestamp: row[0],
            script_name: row[1],
            outcome: row[2],
            cpu_time: row[3],
            duration: row[4],
            event_type: "worker_invocation",
            data_source: "Cloudflare Analytics Engine"
          }));
          total = logs.length;
        } else {
          // 如果Analytics Engine不可用，返回基本信息
          logs = [];
          total = 0;
        }
      } catch (error: any) {
        console.error("Failed to query worker logs:", error);
        logs = [];
        total = 0;
      }

      return c.json({
        logs,
        total,
        limit,
        offset,
        timeRange: query.timeRange,
        success: true,
        message: "Worker日志记录正常运行",
        note: "短链接访问日志正在通过console.log记录，可通过以下方式查看实时日志",
        log_access_methods: {
          wrangler_tail: "运行 'wrangler tail' 查看实时日志流",
          cloudflare_dashboard: "访问 Cloudflare Dashboard > Workers & Pages > mulink > Logs",
          observability: "使用 Cloudflare Observability 查看详细分析"
        },
        current_status: {
          console_logging: "✅ 活跃",
          structured_data: "✅ 包含完整的请求信息（URL、headers、IP等）",
          observability_integration: "✅ 已连接",
          available_fields: availableKeys.length
        },
        sample_log_structure: {
          event_type: "link_redirect | link_not_found",
          slug: "短链接标识",
          timestamp: "Unix时间戳",
          url: "完整请求URL",
          method: "HTTP方法",
          headers: "完整请求头",
          ip: "客户端IP",
          user_agent: "用户代理",
          country: "国家（如果可用）",
          search_params: "URL参数",
          request_id: "唯一请求ID"
        }
      });
    } catch (apiError: any) {
      return c.json(
        {
          logs: [],
          total: 0,
          limit,
          offset,
          timeRange: query.timeRange,
          error: "Failed to fetch logs from Cloudflare API",
          details: apiError.message,
        },
        500
      );
    }
  } catch (error: any) {
    console.error("Failed to fetch logs:", error);
    return c.json(
      {
        error: "Failed to fetch logs",
        details: error.message,
      },
      500
    );
  }
});

// 获取特定请求的详细信息
app.get("/api/log/:requestId", async (c) => {
  try {
    const requestId = c.req.param("requestId");
    const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
    const apiToken = c.env.CLOUDFLARE_API_TOKEN;

    if (!accountId || !apiToken) {
      return c.json(
        {
          error: "Cloudflare API credentials not configured",
        },
        500
      );
    }

    // 尝试通过Workers Trace API查询特定请求
    try {
      const traceResult = await callCloudflareAPI(
        `/accounts/${accountId}/workers/scripts/mulink/traces/${requestId}`,
        accountId,
        apiToken,
        "GET"
      );

      if (traceResult.success) {
        return c.json({
          request_id: requestId,
          trace_data: traceResult.result,
          data_source: "Cloudflare Workers Trace API"
        });
      }
    } catch (traceError: any) {
      console.error("Trace API failed:", traceError);
    }

    // 如果Trace API不可用，返回基本信息
    return c.json(
      {
        error: "Request trace not found or not available",
        request_id: requestId,
        note: "Worker日志可通过Cloudflare Dashboard或wrangler tail查看"
      },
      404
    );
  } catch (error: any) {
    console.error("Failed to fetch short link details:", error);
    return c.json(
      {
        error: "Failed to fetch short link details",
        details: error.message,
      },
      500
    );
  }
});

app.get("/", async (c) => {
  // 记录主页访问事件
  console.log({
    event_type: "homepage_visit",
    timestamp: Date.now(),
    user_agent: c.req.header("user-agent"),
    country: (c.req as any).cf?.country,
    city: (c.req as any).cf?.city,
  });

  return c.redirect("https://fbi.gov");
});

// 通配符路由处理所有slug访问
app.get("/*", async (c) => {
  const url = new URL(c.req.url);
  const slug = url.pathname.slice(1);

  // 跳过空路径和已知路径
  if (!slug || slug.startsWith("api/") || slug.startsWith(".")) {
    return c.text("Not found", 404);
  }

  return handleSlugRedirect(c, slug);
});

function weightPick(targets: { url: string; weight: number }[]) {
  const totalWeight = targets.reduce((acc, cur) => acc + cur.weight, 0);
  const random = Math.random() * totalWeight;
  let currentWeight = 0;
  for (const target of targets) {
    currentWeight += target.weight;
    if (random < currentWeight) {
      return target.url;
    }
  }
  return targets[targets.length - 1].url;
}

function mergeSearchParams(from: string, to: string) {
  const toUrl = new URL(to);
  new URL(from).searchParams.forEach((value, key) => {
    toUrl.searchParams.set(key, value);
  });
  return toUrl.toString();
}

// Cloudflare API工具函数
async function callCloudflareAPI(
  endpoint: string,
  accountId: string,
  apiToken: string,
  method: string = "GET",
  body?: any
): Promise<any> {
  const url = `https://api.cloudflare.com/client/v4/accounts/${accountId}${endpoint}`;

  const response = await fetch(url, {
    method,
    headers: {
      Authorization: `Bearer ${apiToken}`,
      "Content-Type": "application/json",
    },
    body: body ? JSON.stringify(body) : undefined,
  });

  if (!response.ok) {
    console.error("Cloudflare API error:", await response.text());
    throw new Error(
      `Cloudflare API error: ${response.status} ${response.statusText}`
    );
  }

  return await response.json();
}

// 查询Workers Observability日志
async function queryWorkerLogs(
  accountId: string,
  apiToken: string,
  timeframe: {
    from: number;
    to: number;
  }
): Promise<any> {
  // 根据API文档的正确格式，创建临时查询
  const queryBody = {
    timeframe: {
      from: timeframe.from,
      to: timeframe.to,
    },
    // 不提供queryId来创建临时查询
  };

  return await callCloudflareAPI(
    "/workers/observability/telemetry/query",
    accountId,
    apiToken,
    "POST",
    queryBody
  );
}

// 通用的链接处理函数
async function handleSlugRedirect(c: any, slug: string) {
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    // 记录404事件
    console.log({
      event_type: "link_not_found",
      slug,
      timestamp: Date.now(),
      user_agent: c.req.header("user-agent"),
      country: (c.req as any).cf?.country,
      city: (c.req as any).cf?.city,
      referer: c.req.header("referer"),
      ip: c.req.header("CF-Connecting-IP"),
      method: c.req.method,
      url: c.req.url,
      headers: Object.fromEntries(c.req.raw.headers.entries()),
      request_id: crypto.randomUUID(),
    });

    return c.text("Not found", 404);
  }

  const targetsJson = JSON.parse(targets) as { url: string; weight: number }[];
  const pickedUrl = weightPick(targetsJson);
  const finalUrl = mergeSearchParams(c.req.url, pickedUrl);

  // 记录成功跳转事件
  console.log({
    event_type: "link_redirect",
    slug,
    target_url: pickedUrl,
    final_url: finalUrl,
    timestamp: Date.now(),
    user_agent: c.req.header("user-agent"),
    country: (c.req as any).cf?.country,
    city: (c.req as any).cf?.city,
    referer: c.req.header("referer"),
    search_params: Object.fromEntries(
      new URL(c.req.url).searchParams.entries()
    ),
    ip: c.req.header("CF-Connecting-IP"),
    method: c.req.method,
    url: c.req.url,
    headers: Object.fromEntries(c.req.raw.headers.entries()),
    request_id: crypto.randomUUID(),
  });

  // 返回HTTP重定向
  return c.redirect(finalUrl, 302);
}

export default app;
