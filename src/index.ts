import { Hono } from "hono";
import { cors } from "hono/cors";
import { basicAuth } from "hono/basic-auth";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

interface CloudflareBindings {
  KV: KVNamespace;
  // Cloudflare API配置
  CLOUDFLARE_ACCOUNT_ID?: string;
  CLOUDFLARE_API_TOKEN?: string;
}

const app = new Hono<{ Bindings: CloudflareBindings }>();

app.use(
  "/api/*",
  cors({
    origin: (origin) => {
      return origin.endsWith(".melook.app") ||
        origin.includes("localhost") ||
        origin.includes("vusercontent.net")
        ? origin
        : "https://google.com";
    },
    allowMethods: ["GET", "HEAD", "POST", "PUT", "DELETE"],
  })
);

app.use(
  "/api/*",
  basicAuth({
    username: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    password: "TJNIUBI666",
  })
);

app.post(
  "/api/link",
  zValidator(
    "json",
    z.object({
      slug: z.string(),
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const json = c.req.valid("json");
    // 重复校验
    if (await c.env.KV.get(json.slug)) {
      return c.text("Already exists", 400);
    }

    // 记录链接创建事件
    console.log({
      event_type: "link_created",
      slug: json.slug,
      target_count: json.targets.length,
      targets: json.targets.map(t => t.url),
      timestamp: Date.now(),
      user_agent: c.req.header("user-agent")
    });

    await c.env.KV.put(json.slug, JSON.stringify(json.targets), {
      metadata: { createdAt: Date.now() },
    });
    return c.text("OK");
  }
);

app.get("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    return c.text("Not found", 404);
  }

  return c.json({
    slug,
    targets: JSON.parse(targets)
  });
});

app.put(
  "/api/link/:slug",
  zValidator(
    "json",
    z.object({
      targets: z.array(
        z.object({
          url: z.string().url(),
          weight: z.number().min(0).max(100),
        })
      ),
    })
  ),
  async (c) => {
    const slug = c.req.param("slug");
    const json = c.req.valid("json");
    // 存在校验
    if (!(await c.env.KV.get(slug))) {
      return c.text("Not found", 404);
    }

    // 记录链接更新事件
    console.log({
      event_type: "link_updated",
      slug,
      target_count: json.targets.length,
      targets: json.targets.map(t => t.url),
      timestamp: Date.now(),
      user_agent: c.req.header("user-agent")
    });

    await c.env.KV.put(slug, JSON.stringify(json.targets));
    return c.text("OK");
  }
);

app.delete("/api/link/:slug", async (c) => {
  const slug = c.req.param("slug");

  // 检查链接是否存在
  const existingLink = await c.env.KV.get(slug);
  if (!existingLink) {
    return c.text("Not found", 404);
  }

  // 删除KV中的链接数据
  await c.env.KV.delete(slug);

  // 记录链接删除事件
  console.log({
    event_type: "link_deleted",
    slug,
    timestamp: Date.now(),
    user_agent: c.req.header("user-agent")
  });

  return c.text("OK");
});

app.get("/api/links", async (c) => {
  const links = await c.env.KV.list<{ createdAt: number }>();

  return c.json({
    links: links.keys.map((key) => ({
      slug: key.name,
      createdAt: key.metadata?.createdAt,
    })),
  });
});

// 测试Cloudflare API配置
app.get("/api/test-config", async (c) => {
  const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
  const apiToken = c.env.CLOUDFLARE_API_TOKEN;

  return c.json({
    hasAccountId: !!accountId,
    hasApiToken: !!apiToken,
    accountIdFormat: accountId ? {
      length: accountId.length,
      isValidFormat: /^[a-f0-9]{32}$/i.test(accountId),
      preview: accountId.substring(0, 8) + "..." + accountId.substring(accountId.length - 4)
    } : null,
    apiTokenFormat: apiToken ? {
      length: apiToken.length,
      preview: apiToken.substring(0, 8) + "..." + apiToken.substring(apiToken.length - 4)
    } : null
  });
});

// 测试API连接
app.get("/api/test-api", async (c) => {
  try {
    const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
    const apiToken = c.env.CLOUDFLARE_API_TOKEN;

    if (!accountId || !apiToken) {
      return c.json({ error: "Missing credentials" }, 400);
    }

    const result = await testCloudflareAPI(accountId, apiToken);
    return c.json({
      success: true,
      message: "API connection successful",
      keysFound: result.result?.length || 0
    });
  } catch (error: any) {
    return c.json({
      success: false,
      error: error.message,
      details: "API connection failed"
    }, 500);
  }
});

// 获取telemetry keys
app.get("/api/telemetry-keys", async (c) => {
  try {
    const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
    const apiToken = c.env.CLOUDFLARE_API_TOKEN;

    if (!accountId || !apiToken) {
      return c.json({ error: "Missing credentials" }, 400);
    }

    const result = await getTelemetryKeys(accountId, apiToken);
    return c.json({
      success: true,
      keys: result.result || [],
      total: result.result?.length || 0
    });
  } catch (error: any) {
    return c.json({
      success: false,
      error: error.message,
      details: "Failed to get telemetry keys"
    }, 500);
  }
});

// 获取所有请求日志
app.get("/api/logs", async (c) => {
  try {
    const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
    const apiToken = c.env.CLOUDFLARE_API_TOKEN;

    if (!accountId || !apiToken) {
      return c.json({
        error: "Cloudflare API credentials not configured",
        debug: {
          hasAccountId: !!accountId,
          hasApiToken: !!apiToken
        }
      }, 500);
    }

    // 验证Account ID格式（应该是32位十六进制字符串）
    if (!/^[a-f0-9]{32}$/i.test(accountId)) {
      return c.json({
        error: "Invalid Account ID format",
        debug: {
          accountId: accountId.substring(0, 10) + "...",
          expectedFormat: "32-character hexadecimal string"
        }
      }, 400);
    }

    // 获取查询参数
    const limit = parseInt(c.req.query("limit") || "50");
    const offset = parseInt(c.req.query("offset") || "0");
    const slug = c.req.query("slug");
    const eventType = c.req.query("event_type");
    const hours = parseInt(c.req.query("hours") || "24");

    // 构建过滤条件
    const filters: Array<{key: string, operator: string, value: any}> = [];

    if (slug) {
      filters.push({
        key: "slug",
        operator: "equals",
        value: slug
      });
    }

    if (eventType) {
      filters.push({
        key: "event_type",
        operator: "equals",
        value: eventType
      });
    }

    // 构建查询
    const query = {
      limit,
      offset,
      timeRange: {
        start: new Date(Date.now() - hours * 60 * 60 * 1000).toISOString(),
        end: new Date().toISOString()
      },
      filters,
      orderBy: {
        field: "timestamp",
        direction: "desc" as const
      }
    };

    // 由于Observability API可能需要特殊权限或在生产环境中才能使用
    // 我们提供替代方案的说明
    return c.json({
      logs: [],
      total: 0,
      limit,
      offset,
      timeRange: query.timeRange,
      success: false,
      error: "Observability API暂时不可用",
      alternatives: {
        dashboard: {
          description: "通过Cloudflare Dashboard查看日志",
          url: `https://dash.cloudflare.com/da4d4c1dfbeb72e8ae03825ace67946f/workers-and-pages/observability`,
          steps: [
            "1. 访问上述URL",
            "2. 选择您的Worker",
            "3. 查看 'Logs' 或 'Investigate' 标签页",
            "4. 使用Query Builder进行复杂查询"
          ]
        },
        wrangler: {
          description: "使用wrangler命令查看实时日志",
          command: "wrangler tail",
          note: "需要在项目目录中运行"
        },
        production: {
          description: "部署到生产环境后再试",
          note: "Observability API可能只在生产环境中可用"
        }
      },
      recentActivity: {
        message: "基于console.log的结构化日志已启用",
        events: [
          "link_redirect - 链接跳转事件",
          "link_not_found - 404错误事件",
          "link_created - 链接创建事件",
          "link_updated - 链接更新事件",
          "link_deleted - 链接删除事件"
        ]
      }
    });

  } catch (error: any) {
    console.error("Failed to fetch logs:", error);
    return c.json({
      error: "Failed to fetch logs",
      details: error.message
    }, 500);
  }
});

// 获取特定请求的详细信息
app.get("/api/log/:requestId", async (c) => {
  try {
    const accountId = c.env.CLOUDFLARE_ACCOUNT_ID;
    const apiToken = c.env.CLOUDFLARE_API_TOKEN;
    const requestId = c.req.param("requestId");

    if (!accountId || !apiToken) {
      return c.json({
        error: "Cloudflare API credentials not configured"
      }, 500);
    }

    // 查询特定请求ID的日志
    const query = {
      limit: 100,
      offset: 0,
      timeRange: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天前
        end: new Date().toISOString()
      },
      filters: [
        {
          key: "$workers.invocationId",
          operator: "equals",
          value: requestId
        }
      ],
      orderBy: {
        field: "timestamp",
        direction: "asc" as const
      }
    };

    const result = await queryWorkerLogs(accountId, apiToken, query);
    const events = result.data?.events || [];

    if (events.length === 0) {
      return c.json({
        error: "Request not found"
      }, 404);
    }

    // 组织数据：按invocation分组
    const invocationData = {
      requestId,
      events: events,
      summary: {
        totalEvents: events.length,
        startTime: events[0]?.timestamp,
        endTime: events[events.length - 1]?.timestamp,
        duration: events.length > 1 ?
          new Date(events[events.length - 1]?.timestamp).getTime() -
          new Date(events[0]?.timestamp).getTime() : 0
      }
    };

    return c.json(invocationData);

  } catch (error: any) {
    console.error("Failed to fetch request details:", error);
    return c.json({
      error: "Failed to fetch request details",
      details: error.message
    }, 500);
  }
});


app.get("/", async (c) => {
  // 记录主页访问事件
  console.log({
    event_type: "homepage_visit",
    timestamp: Date.now(),
    user_agent: c.req.header("user-agent"),
    country: (c.req as any).cf?.country,
    city: (c.req as any).cf?.city
  });

  return c.redirect("https://fbi.gov");
});

// 通配符路由处理所有slug访问
app.get("/*", async (c) => {
  const url = new URL(c.req.url);
  const slug = url.pathname.slice(1);

  // 跳过空路径和已知路径
  if (!slug || slug.startsWith("api/") || slug.startsWith(".")) {
    return c.text("Not found", 404);
  }

  return handleSlugRedirect(c, slug);
});

function weightPick(targets: { url: string; weight: number }[]) {
  const totalWeight = targets.reduce((acc, cur) => acc + cur.weight, 0);
  const random = Math.random() * totalWeight;
  let currentWeight = 0;
  for (const target of targets) {
    currentWeight += target.weight;
    if (random < currentWeight) {
      return target.url;
    }
  }
  return targets[targets.length - 1].url;
}

function mergeSearchParams(from: string, to: string) {
  const toUrl = new URL(to);
  new URL(from).searchParams.forEach((value, key) => {
    toUrl.searchParams.set(key, value);
  });
  return toUrl.toString();
}

// Cloudflare API工具函数
async function callCloudflareAPI(
  endpoint: string,
  accountId: string,
  apiToken: string,
  method: string = "GET",
  body?: any
): Promise<any> {
  const url = `https://api.cloudflare.com/client/v4/accounts/${accountId}${endpoint}`;

  const response = await fetch(url, {
    method,
    headers: {
      "Authorization": `Bearer ${apiToken}`,
      "Content-Type": "application/json",
    },
    body: body ? JSON.stringify(body) : undefined,
  });

  if (!response.ok) {
    throw new Error(`Cloudflare API error: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}

// 查询Workers Observability日志
async function queryWorkerLogs(
  accountId: string,
  apiToken: string,
  query: {
    limit?: number;
    offset?: number;
    timeRange?: {
      start: string;
      end: string;
    };
    filters?: Array<{
      key: string;
      operator: string;
      value: any;
    }>;
    orderBy?: {
      field: string;
      direction: "asc" | "desc";
    };
  }
): Promise<any> {
  // 尝试更简单的查询格式
  const queryBody = {
    filters: query.filters || [],
    limit: query.limit || 50,
    timeRange: query.timeRange || {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    }
  };

  return await callCloudflareAPI(
    "/workers/observability/telemetry/query",
    accountId,
    apiToken,
    "POST",
    queryBody
  );
}

// 测试API连接的简单函数
async function testCloudflareAPI(accountId: string, apiToken: string): Promise<any> {
  // 先尝试获取账户信息来测试基本连接
  return await callCloudflareAPI(
    "",
    accountId,
    apiToken,
    "GET"
  );
}

// 获取可用的telemetry keys
async function getTelemetryKeys(accountId: string, apiToken: string): Promise<any> {
  return await callCloudflareAPI(
    "/workers/observability/telemetry/keys",
    accountId,
    apiToken,
    "POST",
    {}
  );
}

// 通用的链接处理函数
async function handleSlugRedirect(c: any, slug: string) {
  const targets = await c.env.KV.get(slug);
  if (!targets) {
    // 记录404事件
    console.log({
      event_type: "link_not_found",
      slug,
      timestamp: Date.now(),
      user_agent: c.req.header("user-agent"),
      country: (c.req as any).cf?.country,
      city: (c.req as any).cf?.city,
      referer: c.req.header("referer")
    });
    return c.text("Not found", 404);
  }

  const targetsJson = JSON.parse(targets) as { url: string; weight: number }[];
  const pickedUrl = weightPick(targetsJson);
  const finalUrl = mergeSearchParams(c.req.url, pickedUrl);

  // 记录成功跳转事件
  console.log({
    event_type: "link_redirect",
    slug,
    target_url: pickedUrl,
    final_url: finalUrl,
    timestamp: Date.now(),
    user_agent: c.req.header("user-agent"),
    country: (c.req as any).cf?.country,
    city: (c.req as any).cf?.city,
    referer: c.req.header("referer"),
    search_params: Object.fromEntries(new URL(c.req.url).searchParams.entries())
  });

  // 返回HTTP重定向
  return c.redirect(finalUrl, 302);
}

export default app;
