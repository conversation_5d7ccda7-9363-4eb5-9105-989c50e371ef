/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "mulink",
  "main": "src/index.ts",
  "compatibility_date": "2025-06-20",
  "observability": {
    "enabled": true,
    "logs": {
      "invocation_logs": true,
      "head_sampling_rate": 1
    }
  },
  "placement": { "mode": "smart" },
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "1c05183e56f44957a758ccd8bf525749"
    }
  ]
}
