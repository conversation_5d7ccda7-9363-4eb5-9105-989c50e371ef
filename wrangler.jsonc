/**
 * For more details on how to configure Wrang<PERSON>, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "mulink",
  "main": "src/index.ts",
  "compatibility_date": "2025-06-20",
  "logpush": true,
  "observability": {
    "enabled": true
  },
  "placement": { "mode": "smart" },
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "1c05183e56f44957a758ccd8bf525749"
    }
  ],
  "migrations": [
    {
      "new_sqlite_classes": ["Counter"],
      "tag": "v1"
    }
  ],
  "durable_objects": {
    "bindings": [
      {
        "class_name": "Counter",
        "name": "COUNTER"
      }
    ]
  }
}
