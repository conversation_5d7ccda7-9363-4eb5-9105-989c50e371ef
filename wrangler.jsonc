/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "mulink",
  "main": "src/index.ts",
  "compatibility_date": "2025-06-20",
  "observability": {
    "enabled": true,
    "logs": {
      "invocation_logs": true,
      "head_sampling_rate": 1
    }
  },
  "placement": { "mode": "smart" },
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "1c05183e56f44957a758ccd8bf525749"
    }
  ],
  "vars": {
    "CLOUDFLARE_ACCOUNT_ID": "da4d4c1dfbeb72e8ae03825ace67946f",
    "CLOUDFLARE_API_TOKEN": "****************************************"
  },
  "migrations": [
    {
      "tag": "v2",
      "deleted_classes": ["Counter"]
    }
  ]
}
