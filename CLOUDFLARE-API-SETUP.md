# 🔧 Cloudflare API配置指南

## 📋 概述

为了使用 `/api/logs` 和 `/api/log/:requestId` 接口，您需要配置Cloudflare API凭据。

## 🔑 获取API凭据

### 1. 获取Account ID
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 在右侧边栏找到 **Account ID**（32位十六进制字符串）
3. 复制Account ID

⚠️ **重要**: Account ID应该是类似 `a1b2c3d4e5f6789012345678901234567890abcd` 的32位十六进制字符串，不是邮箱地址！

**如何找到Account ID:**
- 登录Dashboard后，在右侧边栏会显示Account ID
- 或者访问任何页面，URL中包含 `/accounts/{account_id}/` 部分
- Account ID格式: 32个字符，只包含数字0-9和字母a-f

### 2. 创建API Token
1. 访问 [API Tokens页面](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 **Create Token**
3. 选择 **Custom token**
4. 配置权限：
   - **Account** - `Cloudflare Workers:Read`
   - **Zone Resources** - `Include All zones` (如果需要)
5. 点击 **Continue to summary**
6. 点击 **Create Token**
7. 复制生成的Token

## ⚙️ 配置环境变量

### 本地开发
创建 `.dev.vars` 文件：
```bash
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_API_TOKEN=your_api_token_here
```

### 生产环境
使用wrangler命令设置secrets：
```bash
# 设置Account ID
wrangler secret put CLOUDFLARE_ACCOUNT_ID

# 设置API Token
wrangler secret put CLOUDFLARE_API_TOKEN
```

或者在Cloudflare Dashboard中设置：
1. 进入 Workers & Pages
2. 选择您的Worker
3. 进入 Settings > Variables
4. 添加环境变量

## 🚀 API使用示例

### 获取所有日志
```bash
# 获取最近24小时的所有日志
curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
  "https://your-worker.workers.dev/api/logs"

# 获取特定slug的日志
curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
  "https://your-worker.workers.dev/api/logs?slug=example-slug"

# 获取特定事件类型的日志
curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
  "https://your-worker.workers.dev/api/logs?event_type=link_redirect"

# 分页查询
curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
  "https://your-worker.workers.dev/api/logs?limit=20&offset=0"

# 指定时间范围（小时）
curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
  "https://your-worker.workers.dev/api/logs?hours=48"
```

### 获取特定请求详情
```bash
curl -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
  "https://your-worker.workers.dev/api/log/your-request-id"
```

## 📊 API响应格式

### GET /api/logs
```json
{
  "logs": [
    {
      "timestamp": "2025-01-09T10:30:00.000Z",
      "event_type": "link_redirect",
      "slug": "example-slug",
      "target_url": "https://example.com",
      "country": "US",
      "city": "San Francisco",
      "user_agent": "Mozilla/5.0...",
      "referer": "https://google.com"
    }
  ],
  "total": 150,
  "limit": 50,
  "offset": 0,
  "timeRange": {
    "start": "2025-01-08T10:30:00.000Z",
    "end": "2025-01-09T10:30:00.000Z"
  }
}
```

### GET /api/log/:requestId
```json
{
  "requestId": "abc123def456",
  "events": [
    {
      "timestamp": "2025-01-09T10:30:00.000Z",
      "level": "info",
      "message": "Request started",
      "metadata": {...}
    }
  ],
  "summary": {
    "totalEvents": 5,
    "startTime": "2025-01-09T10:30:00.000Z",
    "endTime": "2025-01-09T10:30:01.234Z",
    "duration": 1234
  }
}
```

## 🔍 查询参数

### /api/logs 支持的参数
- `limit`: 返回记录数量 (默认: 50)
- `offset`: 偏移量 (默认: 0)
- `slug`: 过滤特定slug
- `event_type`: 过滤特定事件类型
- `hours`: 时间范围（小时，默认: 24）

## 🛡️ 权限要求

API接口需要基本认证：
- Username: `MULINK`
- Password: `TJNIUBI666`

## ⚠️ 注意事项

1. **API限制**: Cloudflare API有速率限制，请合理使用
2. **数据保留**: 日志数据保留期限取决于您的Cloudflare计划
3. **权限**: 确保API Token有足够的权限访问Workers Observability数据
4. **安全**: 不要在代码中硬编码API凭据，使用环境变量

## 🔧 故障排除

### 常见错误
1. **401 Unauthorized**: 检查API Token是否正确
2. **403 Forbidden**: 检查API Token权限
3. **404 Not Found**: 检查Account ID是否正确
4. **500 Internal Server Error**: 检查环境变量配置

### 调试步骤
1. 验证Account ID和API Token
2. 检查API Token权限
3. 查看Worker日志获取详细错误信息
4. 使用 `wrangler tail` 查看实时日志
