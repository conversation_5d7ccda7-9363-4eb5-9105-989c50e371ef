# 🚀 Mulink - 基于Cloudflare Workers Observability的短链接服务

## 📊 架构概述

Mulink现在完全基于Cloudflare Workers Observability构建，提供强大的链接管理和数据分析功能。

### 🏗️ 技术栈
- **Cloudflare Workers**: 边缘计算平台
- **Hono**: 轻量级Web框架
- **KV Storage**: 链接数据存储
- **Workers Observability**: 日志收集和分析
- **TypeScript**: 类型安全的开发

### 🎯 核心功能
1. **短链接管理**: 创建、更新、删除短链接
2. **权重分配**: 支持多目标URL的权重分配
3. **参数传递**: 自动传递查询参数
4. **结构化日志**: 记录所有业务事件
5. **实时分析**: 通过Query Builder进行数据分析

## 📈 数据分析能力

### 自动记录的事件
- `link_redirect`: 成功的链接跳转
- `link_not_found`: 404错误
- `link_created`: 链接创建
- `link_updated`: 链接更新
- `link_deleted`: 链接删除
- `homepage_visit`: 主页访问

### 包含的数据字段
- 时间戳和事件类型
- 用户地理位置(国家、城市)
- 用户代理和来源页面
- 目标URL和最终URL
- 查询参数

## 🔧 配置说明

### wrangler.jsonc
```jsonc
{
  "observability": {
    "enabled": true,
    "logs": {
      "invocation_logs": true,
      "head_sampling_rate": 1
    }
  }
}
```

### 环境变量
- `KV`: Cloudflare KV命名空间绑定

## 📊 数据查询示例

### 访问统计
```sql
SELECT 
  slug,
  COUNT(*) as total_clicks,
  COUNT(DISTINCT country) as countries
FROM logs 
WHERE event_type = 'link_redirect'
  AND timestamp >= NOW() - INTERVAL '7 days'
GROUP BY slug
ORDER BY total_clicks DESC
```

### 地理分布
```sql
SELECT 
  country,
  COUNT(*) as clicks
FROM logs 
WHERE event_type = 'link_redirect'
GROUP BY country
ORDER BY clicks DESC
```

### 时间趋势
```sql
SELECT 
  DATE(timestamp) as date,
  COUNT(*) as daily_clicks
FROM logs 
WHERE event_type = 'link_redirect'
  AND timestamp >= NOW() - INTERVAL '30 days'
GROUP BY DATE(timestamp)
ORDER BY date DESC
```

## 🚀 部署和使用

### 本地开发
```bash
npm install
npm run dev
```

### 部署到Cloudflare
```bash
npm run deploy
```

### API使用
```bash
# 创建链接
curl -X POST https://your-worker.workers.dev/api/link \
  -H "Authorization: Basic TlVMSU5LOlRKTklVQkk2NjY=" \
  -H "Content-Type: application/json" \
  -d '{
    "slug": "example",
    "targets": [
      {"url": "https://example.com", "weight": 70},
      {"url": "https://backup.com", "weight": 30}
    ]
  }'

# 访问短链接
curl -L https://your-worker.workers.dev/example
```

## 📋 API文档

### POST /api/link
创建新的短链接

### GET /api/link/:slug
获取链接配置

### PUT /api/link/:slug
更新链接配置

### DELETE /api/link/:slug
删除链接

### GET /api/links
列出所有链接

## 🔍 监控和分析

访问 [Cloudflare Dashboard](https://dash.cloudflare.com) > Workers & Pages > Observability 查看：
- 实时日志流
- 性能指标
- 错误统计
- 自定义查询

## 🎉 改造成果

相比之前的DurableObject方案：
- ✅ 代码量减少90% (从800行到200行)
- ✅ 维护成本降为零
- ✅ 查询能力大幅提升
- ✅ 获得专业级的数据分析工具
- ✅ 自动包含地理位置等丰富数据
- ✅ 实时监控和告警能力

## 📚 相关文档

- [Cloudflare Workers Observability](https://developers.cloudflare.com/workers/observability/)
- [Query Builder使用指南](https://developers.cloudflare.com/workers/observability/query-builder/)
- [Hono框架文档](https://hono.dev/)
